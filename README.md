# Dr. Club

## Tech Stack

- **Backend**: Laravel 12 (PHP 8.2+)
- **Frontend**: React 19 with TypeScript
- **Database**: PostgreSQL 17
- **Styling**: Tailwind CSS 4.0
- **Build Tool**: Vite 6.0
- **Development**: <PERSON><PERSON> Sail (Docker)
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React

## Prerequisites

- Docker and Docker Compose
- Git

## Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd dr-club
```

### 2. Environment Setup

Copy the environment file and configure it:

```bash
cp .env.docker .env
```

### 3. Install Dependencies

Install PHP dependencies:

```bash
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php84-composer:latest \
    composer install --ignore-platform-reqs
```

### 4. Start the Application with Sail

Start all services (Laravel app, PostgreSQL, Mailpit):

```bash
./vendor/bin/sail up -d
```

Or use the alias (recommended):

```bash
# Create an alias for easier usage
alias sail='./vendor/bin/sail'
sail up -d
```

### 5. Application Setup

Generate application key:

```bash
sail artisan key:generate
```

Run database migrations:

```bash
sail artisan migrate
```

Install Node.js dependencies:

```bash
sail npm install
```

### 6. Build Frontend Assets

For development (with hot reload):

```bash
sail npm run dev
```

For production:

```bash
sail npm run build
```

## Running the Application

### Development Mode

Start all services and development servers:

```bash
# Start Docker containers
sail up -d

# Start Vite development server (in another terminal)
sail npm run dev
```

The application will be available at:
- **Main App**: http://localhost
- **Mailpit (Email testing)**: http://localhost:8025
- **Database**: localhost:5432

### Using Composer Scripts

The application includes convenient Composer scripts for development:

```bash
# Start all development services (Laravel server, queue, logs, Vite)
sail composer dev

# Run with SSR support
sail composer dev:ssr

# Run tests
sail composer test
```

### Production Mode

For production deployment:

```bash
# Build frontend assets
sail npm run build

# Optimize Laravel
sail artisan config:cache
sail artisan route:cache
sail artisan view:cache
```

## Available Services

When running with Sail, the following services are available:

| Service | URL | Description |
|---------|-----|-------------|
| Laravel App | http://localhost | Main application |
| PostgreSQL | localhost:5432 | Database server |
| Mailpit | http://localhost:8025 | Email testing interface |

## Development Commands

### Laravel Sail Commands

```bash
# Start services
sail up -d

# Stop services
sail down

# View logs
sail logs

# Access Laravel container shell
sail shell

# Run Artisan commands
sail artisan <command>

# Run Composer commands
sail composer <command>

# Run NPM commands
sail npm <command>

# Run tests
sail test
```

### Database Commands

```bash
# Run migrations
sail artisan migrate

# Rollback migrations
sail artisan migrate:rollback

# Seed database
sail artisan db:seed

# Fresh migration with seeding
sail artisan migrate:fresh --seed
```

### Frontend Commands

```bash
# Install dependencies
sail npm install

# Development server
sail npm run dev

# Build for production
sail npm run build

# Build with SSR
sail npm run build:ssr

# Lint code
sail npm run lint

# Format code
sail npm run format

# Type checking
sail npm run types
```

## Testing

Run the test suite:

```bash
# Run all tests
sail test

# Run specific test file
sail test tests/Feature/ExampleTest.php

# Run with coverage
sail test --coverage
```